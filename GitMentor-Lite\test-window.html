<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tauri窗口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 4px;
            border-left: 4px solid #007acc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Tauri窗口功能测试</h1>
        <p>测试WindowManager和DiffViewer窗口功能</p>
        
        <div>
            <button onclick="testWindowManager()">测试WindowManager</button>
            <button onclick="openDiffViewer()">打开差异查看器</button>
            <button onclick="testMultipleWindows()">测试多窗口</button>
        </div>
        
        <div id="result" class="result" style="display: none;">
            <h3>测试结果：</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const result = document.getElementById('result');
            const content = document.getElementById('resultContent');
            result.style.display = 'block';
            content.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
        }

        async function testWindowManager() {
            try {
                log('开始测试WindowManager...');
                
                // 动态导入WindowManager
                const { default: WindowManager } = await import('./src/utils/WindowManager.ts');
                log('✅ WindowManager导入成功');
                
                // 测试打开窗口
                await WindowManager.openDiffViewer('test/file.js', 'WorkingTree');
                log('✅ 差异查看器窗口创建成功');
                
            } catch (error) {
                log('❌ 测试失败: ' + error.message);
                console.error('测试错误:', error);
            }
        }

        async function openDiffViewer() {
            try {
                log('打开差异查看器窗口...');
                
                const { default: WindowManager } = await import('./src/utils/WindowManager.ts');
                await WindowManager.openDiffViewer('src/components/GitPanel.vue', 'HeadToWorking');
                log('✅ 差异查看器窗口已打开');
                
            } catch (error) {
                log('❌ 打开失败: ' + error.message);
                console.error('错误:', error);
            }
        }

        async function testMultipleWindows() {
            try {
                log('测试多窗口功能...');
                
                const { default: WindowManager } = await import('./src/utils/WindowManager.ts');
                
                // 打开多个不同文件的差异窗口
                await WindowManager.openDiffViewer('file1.js', 'WorkingTree');
                await WindowManager.openDiffViewer('file2.vue', 'Staged');
                await WindowManager.openDiffViewer('file3.ts', 'HeadToWorking');
                
                log('✅ 多个差异查看器窗口已创建');
                log('窗口数量: ' + WindowManager.getDiffViewerCount());
                
            } catch (error) {
                log('❌ 多窗口测试失败: ' + error.message);
                console.error('错误:', error);
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('DOMContentLoaded', () => {
            log('🚀 测试页面已加载，可以开始测试');
        });
    </script>
</body>
</html>
