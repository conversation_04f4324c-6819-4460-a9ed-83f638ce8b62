{"$schema": "https://schema.tauri.app/config/2", "productName": "gitmentor-lite", "version": "0.1.0", "identifier": "com.gitmentor-lite.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "GitMentor Lite", "width": 800, "height": 600}], "security": {"csp": null, "capabilities": [{"identifier": "main-capability", "description": "Main window capabilities", "windows": ["main"], "permissions": ["core:default", "core:webview:allow-create-webview-window", "core:webview:allow-webview-close", "core:webview:allow-webview-position", "core:webview:allow-webview-size", "core:window:allow-close", "core:window:allow-center", "core:window:allow-set-focus", "core:window:allow-set-title", "core:window:allow-set-size", "core:window:allow-set-resizable"]}]}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}