# Git差异对比功能测试指南

## 快速测试步骤

### 1. 准备测试环境
1. 确保GitMentor-Lite应用正在运行
2. 打开一个包含Git仓库的文件夹
3. 确保仓库中有一些文件可以进行修改

### 2. 创建测试文件差异

#### 方法一：修改现有文件
```bash
# 在仓库中修改一个现有文件
echo "// 这是一行新增的注释" >> src/main.ts
```

#### 方法二：创建新文件
```bash
# 创建一个新的测试文件
echo "console.log('Hello, Diff Viewer!');" > test-diff.js
```

### 3. 测试差异查看功能

#### 测试工作区差异
1. 修改文件后，不要暂存更改
2. 在GitMentor-Lite的"更改"区域找到修改的文件
3. 点击文件右侧的👁️图标
4. **预期结果**：显示工作区与暂存区的差异

#### 测试暂存区差异
1. 将修改的文件添加到暂存区（点击➕按钮）
2. 在"暂存的更改"区域找到文件
3. 点击文件右侧的👁️图标
4. **预期结果**：显示暂存区与HEAD的差异

#### 测试新文件
1. 创建一个新文件（如上面的test-diff.js）
2. 在"更改"区域找到新文件（标记为❓）
3. 点击👁️图标
4. **预期结果**：显示完整的文件内容，标记为"新文件"

### 4. 测试界面功能

#### 视图切换测试
1. 打开任意文件的差异查看器
2. 点击📄按钮切换到统一视图
3. 点击📋按钮切换回并排视图
4. **预期结果**：视图正确切换，内容保持一致

#### 代码换行测试
1. 打开包含长行代码的文件差异
2. 点击📐按钮启用换行
3. 点击📏按钮禁用换行
4. **预期结果**：长行代码的显示方式正确切换

#### 语法高亮测试
1. 测试不同类型的文件：
   - JavaScript/TypeScript文件
   - Vue组件文件
   - CSS/SCSS文件
   - Markdown文件
2. **预期结果**：每种文件类型都有相应的语法高亮

### 5. 边界情况测试

#### 二进制文件测试
1. 添加一个图片文件到仓库
2. 尝试查看其差异
3. **预期结果**：显示"二进制文件"提示，不显示内容

#### 大文件测试
1. 创建一个较大的文本文件（>100行）
2. 修改其中几行
3. 查看差异
4. **预期结果**：正确显示差异，性能可接受

#### 删除文件测试
1. 删除一个已跟踪的文件
2. 在Git状态中找到删除的文件
3. 尝试查看差异
4. **预期结果**：正确处理删除文件的情况

### 6. 错误处理测试

#### 无效文件路径
1. 手动触发一个不存在文件的差异查看
2. **预期结果**：显示错误信息，提供重试选项

#### 权限问题
1. 尝试查看没有读取权限的文件
2. **预期结果**：显示相应的错误信息

### 7. 性能测试

#### 响应时间测试
1. 测试小文件（<10KB）的差异加载时间
2. 测试中等文件（10KB-100KB）的差异加载时间
3. **预期结果**：
   - 小文件：<1秒
   - 中等文件：<3秒

#### 内存使用测试
1. 连续打开多个文件的差异查看器
2. 关闭查看器后检查内存是否正确释放
3. **预期结果**：无明显内存泄漏

## 验证清单

### ✅ 基础功能
- [ ] 工作区差异正确显示
- [ ] 暂存区差异正确显示
- [ ] 新文件差异正确显示
- [ ] 删除文件正确处理

### ✅ 界面交互
- [ ] 并排视图正常工作
- [ ] 统一视图正常工作
- [ ] 视图切换流畅
- [ ] 代码换行功能正常
- [ ] 关闭按钮正常工作

### ✅ 语法高亮
- [ ] JavaScript文件高亮正确
- [ ] TypeScript文件高亮正确
- [ ] Vue文件高亮正确
- [ ] CSS文件高亮正确
- [ ] 其他支持的文件类型高亮正确

### ✅ 错误处理
- [ ] 二进制文件正确识别
- [ ] 错误信息正确显示
- [ ] 重试功能正常工作
- [ ] 加载状态正确显示

### ✅ 性能表现
- [ ] 小文件加载快速
- [ ] 大文件处理合理
- [ ] 内存使用正常
- [ ] 无明显卡顿

## 报告问题

如果在测试过程中发现问题，请记录以下信息：

1. **问题描述**：具体的错误现象
2. **重现步骤**：如何触发该问题
3. **文件类型**：测试的文件类型和大小
4. **错误信息**：控制台或界面显示的错误信息
5. **环境信息**：操作系统、Git版本等

---

**测试完成后，Git差异对比功能应该能够：**
- 正确显示各种类型的文件差异
- 提供流畅的用户交互体验
- 处理各种边界情况和错误
- 保持良好的性能表现

**作者**：Evilek  
**编写日期**：2025-01-18
