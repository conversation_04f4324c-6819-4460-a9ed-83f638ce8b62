{"rust-analyzer.cargo.target": "x86_64-pc-windows-msvc", "rust-analyzer.checkOnSave.command": "clippy", "rust-analyzer.cargo.features": "all", "typescript.preferences.importModuleSpecifier": "relative", "vue.codeActions.enabled": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[rust]": {"editor.defaultFormatter": "rust-lang.rust-analyzer", "editor.tabSize": 4}, "[vue]": {"editor.defaultFormatter": "vue.volar"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "files.associations": {"*.toml": "toml", "tauri.conf.json": "json"}, "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000, "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}}, "git.enableSmartCommit": true, "git.confirmSync": false, "explorer.confirmDelete": false, "explorer.confirmDragAndDrop": false, "workbench.startupEditor": "readme", "search.exclude": {"**/node_modules": true, "**/target": true, "**/dist": true, "**/.git": true}, "files.exclude": {"**/node_modules": true, "**/target/debug": true, "**/target/release": true}, "emmet.includeLanguages": {"vue": "html"}}