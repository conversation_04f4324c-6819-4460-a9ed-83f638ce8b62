# Git文件差异对比功能使用指南

## 功能概述

GitMentor-Lite现在支持完整的Git文件差异对比功能，提供类似VSCode的差异查看体验。

## 主要特性

### ✨ 核心功能
- **并排差异查看器**：左右对比显示文件的旧版本和新版本
- **统一差异视图**：传统的统一diff格式显示
- **语法高亮**：支持多种编程语言的语法高亮
- **智能差异类型**：根据文件状态自动选择合适的差异类型
- **新窗口显示**：在独立的弹窗中显示差异，不影响主界面

### 🎯 支持的差异类型
1. **工作区差异** (`WorkingTree`)：工作区文件与暂存区的差异
2. **暂存区差异** (`Staged`)：暂存区文件与HEAD提交的差异  
3. **完整差异** (`HeadToWorking`)：HEAD提交与工作区文件的差异

### 🔧 交互功能
- **视图切换**：点击📄/📋按钮在并排视图和统一视图间切换
- **代码换行**：点击📏/📐按钮启用/禁用代码自动换行
- **关闭查看器**：点击✕按钮关闭差异查看器

## 使用方法

### 1. 基本操作
1. 在GitMentor-Lite中打开一个Git仓库
2. 在文件列表中找到有更改的文件
3. 点击文件右侧的👁️（小眼睛）图标
4. 差异查看器将在新窗口中打开

### 2. 差异类型说明

#### 暂存区文件
- 对于已暂存的文件，显示**暂存区与HEAD**的差异
- 帮助您了解即将提交的更改内容

#### 工作区文件  
- 对于未暂存的文件，显示**工作区与暂存区**的差异
- 帮助您了解当前的工作进度

#### 未跟踪文件
- 新文件将显示完整的文件内容
- 标记为"新文件"状态

### 3. 界面说明

#### 头部工具栏
- **文件路径**：显示当前查看的文件路径
- **文件状态标签**：
  - 🟢 `新文件`：新增的文件
  - 🔴 `已删除`：被删除的文件  
  - 🟣 `二进制文件`：二进制文件（无法显示差异）
- **语言标识**：显示文件的编程语言类型

#### 控制按钮
- **📄/📋**：切换并排视图/统一视图
- **📏/📐**：启用/禁用代码换行
- **✕**：关闭差异查看器

## 支持的文件类型

### 编程语言（支持语法高亮）
- **前端**：JavaScript, TypeScript, Vue, HTML, CSS, SCSS, Less
- **后端**：Rust, Python, Java, C/C++, C#, Go, PHP, Ruby
- **其他**：JSON, XML, YAML, TOML, Markdown, SQL, Shell脚本

### 二进制文件检测
自动检测并标记以下类型的二进制文件：
- 可执行文件：exe, dll, so, dylib
- 图片文件：jpg, png, gif, bmp, ico, svg
- 媒体文件：mp3, mp4, avi, mov, wav
- 压缩文件：zip, rar, 7z, tar, gz
- 文档文件：pdf, doc, docx, xls, xlsx, ppt, pptx

## 技术实现

### 后端API
- **命令**：`get_file_diff`
- **参数**：文件路径和差异类型
- **返回**：文件内容、语言类型、差异信息等

### 前端组件
- **DiffViewer.vue**：主要的差异查看器组件
- **基于**：@git-diff-view/vue 库
- **样式**：类似GitHub的差异显示风格

## 故障排除

### 常见问题

1. **差异查看器无法打开**
   - 确保文件不是未跟踪的二进制文件
   - 检查文件是否存在读取权限

2. **语法高亮不显示**
   - 确认文件扩展名是否在支持列表中
   - 检查文件内容是否为有效的文本格式

3. **差异内容为空**
   - 确认文件确实有更改
   - 检查Git仓库状态是否正常

### 性能优化
- 大文件（>1MB）可能加载较慢
- 二进制文件会被自动跳过以提高性能
- 差异计算在前端进行，减少后端负载

## 更新日志

### v0.1.0 (2025-01-18)
- ✅ 实现基础的文件差异查看功能
- ✅ 支持并排和统一差异视图
- ✅ 添加语法高亮支持
- ✅ 实现智能差异类型检测
- ✅ 添加二进制文件检测
- ✅ 集成到主界面的文件列表

---

**作者**：Evilek  
**编写日期**：2025-01-18  
**版本**：v0.1.0
