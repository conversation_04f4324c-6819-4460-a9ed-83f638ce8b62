{"name": "gitmentor-lite", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@git-diff-view/core": "^0.0.30", "@git-diff-view/file": "^0.0.30", "@git-diff-view/vue": "^0.0.30", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-opener": "^2", "element-plus": "^2.10.1", "jsdiff": "^1.1.1", "pinia": "^3.0.3", "vue": "^3.5.13", "vue-diff": "^1.2.4", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "typescript": "~5.6.2", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}